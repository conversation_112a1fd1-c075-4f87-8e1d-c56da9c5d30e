{"extensionName": {"message": "Smart Bookmarker", "description": "The name of the extension."}, "extensionDescription": {"message": "An intelligent bookmark management tool that uses AI to automatically categorize, summarize, and tag your bookmarks.", "description": "The description of the extension."}, "addCurrentPage": {"message": "Bookmark Current Page", "description": "Button text to add the current page as a bookmark."}, "settings": {"message": "Full List & Settings", "description": "Button text to open the options page."}, "allBookmarks": {"message": "All", "description": "Tab title for all bookmarks."}, "starredBookmarks": {"message": "Starred", "description": "Tab title for starred bookmarks."}, "noBookmarks": {"message": "No bookmarks yet. Add some!", "description": "Message shown when the bookmark list is empty."}, "processing": {"message": "Processing...", "description": "Toast message indicating an operation is in progress."}, "taskQueued": {"message": "AI analysis task has been queued.", "description": "Toast message when a new bookmark is added and queued for AI processing."}, "pageExists": {"message": "This page is already bookmarked.", "description": "Toast message when trying to add a duplicate bookmark."}, "noActiveTab": {"message": "Could not find an active page to bookmark.", "description": "Error message when no active tab is available."}, "operationFailed": {"message": "Operation failed. Please try again.", "description": "Generic error message for a failed operation."}, "aiProcessing": {"message": "AI Analyzing...", "description": "Status text for a bookmark being processed by AI."}, "aiFailed": {"message": "AI analysis failed.", "description": "Status text when AI analysis has failed for a bookmark."}, "optionsTitle": {"message": "My Smart Bookmarks", "description": "Title for the options page."}, "importBookmarks": {"message": "Import from Browser", "description": "Button to import bookmarks from the browser."}, "aiConfig": {"message": "AI Configuration", "description": "Button and modal title for AI settings."}, "qaSystem": {"message": "Smart Q&A", "description": "Button and modal title for the Q&A system."}, "searchPlaceholder": {"message": "Search in title, summary, category, URL...", "description": "Placeholder for the search input on the options page."}, "close": {"message": "Close", "description": "Title for the close button on modals."}, "aiProvider": {"message": "AI Provider", "description": "Label for the AI provider selector."}, "apiKey": {"message": "API Key", "description": "Label for the API key input."}, "model": {"message": "Model", "description": "Label for the AI model selector."}, "saveConfig": {"message": "Save Configuration", "description": "Button to save AI configuration."}, "configSaved": {"message": "Configuration saved successfully!", "description": "Toast message when AI config is saved."}, "languageChanged": {"message": "Language setting changed. Reloading...", "description": "Toast message when language is changed."}, "importSuccess": {"message": "Successfully imported $count$ new bookmarks. AI analysis is in progress.", "description": "Toast message for successful import.", "placeholders": {"count": {"content": "$1", "example": "5"}}}, "importFailed": {"message": "Bookmark import failed.", "description": "Toast message for failed import."}, "importNoNew": {"message": "No new bookmarks to import.", "description": "Toast message when there are no new bookmarks to import."}, "toggleStar": {"message": "Toggle Star", "description": "Tooltip for the star button."}, "regenerateAI": {"message": "Regenerate AI Data", "description": "Tooltip for the regenerate button."}, "delete": {"message": "Delete Bookmark", "description": "Tooltip for the delete button."}, "confirmDelete": {"message": "Are you sure you want to delete this bookmark?", "description": "Confirmation message before deleting a bookmark."}, "bookmarkDeleted": {"message": "Bookmark deleted.", "description": "Toast message when a bookmark is deleted."}, "regenerateRequestSent": {"message": "Request to regenerate AI data has been sent.", "description": "Toast message when regenerate is clicked."}, "taskAlreadyQueued": {"message": "This task is already in the queue.", "description": "Toast message for a task that is already queued."}, "aiRegenerateStarted": {"message": "AI regeneration process has started.", "description": "Toast message when AI regeneration begins."}, "keyPoints": {"message": "Key Points", "description": "Label for the key points section."}, "contentType": {"message": "Content Type", "description": "Label for content type."}, "readingTime": {"message": "Est. <PERSON>", "description": "Label for estimated reading time."}, "readingLevel": {"message": "Reading Level", "description": "Label for reading level."}, "minutes": {"message": " min", "description": "Unit for minutes."}, "contentType_article": {"message": "Article"}, "contentType_tutorial": {"message": "Tutorial"}, "contentType_news": {"message": "News"}, "contentType_reference": {"message": "Reference"}, "contentType_tool": {"message": "Tool"}, "contentType_entertainment": {"message": "Entertainment"}, "contentType_blog": {"message": "Blog Post"}, "contentType_documentation": {"message": "Documentation"}, "contentType_research": {"message": "Research"}, "readingLevel_beginner": {"message": "<PERSON><PERSON><PERSON>"}, "readingLevel_intermediate": {"message": "Intermediate"}, "readingLevel_advanced": {"message": "Advanced"}, "rootFolder": {"message": "All Bookmarks", "description": "Breadcrumb text for the root folder."}, "searchResults": {"message": "Search Results", "description": "Breadcrumb text for search results."}, "noMatchingBookmarks": {"message": "No bookmarks match your search.", "description": "Empty state text for search with no results."}, "folderEmpty": {"message": "This folder is empty.", "description": "Empty state text for an empty folder."}, "deleteFolder": {"message": "Delete Folder", "description": "Context menu item to delete a folder."}, "confirmDeleteFolder": {"message": "Are you sure you want to delete the folder '$folderName$' and all its contents? This action cannot be undone.", "description": "Confirmation message before deleting a folder.", "placeholders": {"folderName": {"content": "$1"}}}, "addToBookmarks": {"message": "Add to Bookmarks", "description": "Button to add a recommended site to bookmarks."}, "aiAnswer": {"message": "AI Answer", "description": "Header for the AI's answer in the Q&A modal."}, "alreadyBookmarked": {"message": "Bookmarked", "description": "Label indicating a recommended site is already in the bookmarks."}, "askQuestion": {"message": "Ask", "description": "<PERSON>ton text to submit a question in the Q&A modal."}, "cancel": {"message": "Cancel", "description": "But<PERSON> text for canceling an action."}, "contextMenuTitle": {"message": "Bookmark with Smart Bookmarker", "description": "Title for the context menu item."}, "editNotes": {"message": "Edit Notes", "description": "Tooltip for the button to edit notes."}, "folderDeleted": {"message": "Folder and its contents have been deleted.", "description": "Toast message when a folder is deleted."}, "newRecommendations": {"message": "New Recommendations For You", "description": "Header for new website recommendations in Q&A."}, "notesPlaceholder": {"message": "Enter your private notes here (e.g., account, password). They are stored locally and will not be uploaded.", "description": "Placeholder text for the notes textarea."}, "notesSaved": {"message": "Notes saved successfully!", "description": "Toast message shown after notes are saved."}, "practicalTips": {"message": "Practical Tips", "description": "Header for practical tips in Q&A."}, "qaFeature1": {"message": "Understand your questions in natural language.", "description": "Feature 1 of the Q&A system."}, "qaFeature2": {"message": "Find the most relevant bookmarks you've saved.", "description": "Feature 2 of the Q&A system."}, "qaFeature3": {"message": "Provide direct answers and summaries.", "description": "Feature 3 of the Q&A system."}, "qaFeature4": {"message": "Recommend new websites based on your interests.", "description": "Feature 4 of the Q&A system."}, "qaQuestionLabel": {"message": "My Question:", "description": "Label for the question input text area in the Q&A modal."}, "qaSearchingBookmarks": {"message": "Searching your bookmarks...", "description": "Loading message shown after a question is asked."}, "qaSystemDescription": {"message": "This system helps you find answers and recommendations based on your bookmarks. It can:", "description": "A short description of what the Q&A system does."}, "qaSystemTitle": {"message": "Welcome to Smart Q&A", "description": "The main title for the Q&A system info box."}, "qaTip": {"message": "Tip: Try asking things like 'How to learn JavaScript?' or 'Recommend some design tools.'", "description": "A helpful tip for users on what to ask."}, "questionPlaceholder": {"message": "Enter your question, e.g., 'tools for PDF to PPT'", "description": "Placeholder text for the question input in the Q&A modal."}, "recommendationReason": {"message": "Reason", "description": "Label for the reason why a site is recommended."}, "relatedInBookmarks": {"message": "Related in Your Bookmarks", "description": "Header for existing bookmarks related to the Q&A."}, "resetQA": {"message": "Ask Another Question", "description": "<PERSON><PERSON> to reset the Q&A interface to ask a new question."}, "save": {"message": "Save", "description": "Button text for saving."}, "summaryMissing": {"message": "Summary not generated."}, "tagsMissing": {"message": "Tags not generated."}, "verifying": {"message": "Verifying", "description": "Label indicating a recommended URL is being verified."}, "visitSite": {"message": "Visit Site", "description": "Button to visit a recommended website."}, "analysisDepth": {"message": "Analysis Depth", "description": "Label for the AI analysis depth selection dropdown."}, "analysisDepthBasic": {"message": "Basic (Summary & Tags)", "description": "The 'Basic' option for AI analysis depth."}, "analysisDepthStandard": {"message": "Standard (Basic + Category & Key Points)", "description": "The 'Standard' option for AI analysis depth."}, "analysisDepthDetailed": {"message": "Detailed (Standard + Content Type, Reading Level, etc.)", "description": "The 'Detailed' option for AI analysis depth."}, "analysisDepthHelp": {"message": "Deeper analysis provides more details but consumes more API quota.", "description": "Help text explaining the trade-off of analysis depth."}, "learningAssistantFallbackTitle": {"message": "🎓 Learning Assistant (Fallback Mode)", "description": "Title for the learning assistant panel when injected as a fallback."}, "askAboutArticle": {"message": "Ask about this article:", "description": "Label for the question input section."}, "qaInputPlaceholder": {"message": "e.g., What is the core algorithm mentioned in the text?", "description": "Placeholder text for the question textarea."}, "askButton": {"message": "Ask", "description": "Text for the button that submits a question."}, "analyzingAndThinking": {"message": "🤖 Analyzing the article and thinking of an answer...", "description": "Loading message shown while waiting for a question's answer."}, "errorPrefix": {"message": "Error", "description": "Prefix for error messages."}, "generateQuiz": {"message": "Generate a learning quiz:", "description": "Label for the quiz generation section."}, "generateQuizButton": {"message": "Click to generate 3-5 key questions", "description": "Text for the button that generates a quiz."}, "readingAndQuizzing": {"message": "🤖 Reading the article and preparing questions for you...", "description": "Loading message shown while generating a quiz."}, "viewAnswer": {"message": "View Answer", "description": "Text for the details/summary element to reveal a quiz answer."}, "learningAssistant": {"message": "Learning Assistant", "description": "Tooltip for the Learning Assistant button."}, "restartAiTasks": {"message": "Restart AI Analysis", "description": "<PERSON><PERSON> to restart all pending or failed AI tasks."}, "restartAiTasksTooltip": {"message": "Force re-analysis of all processing or failed bookmarks.", "description": "Tooltip for the Restart AI Analysis button."}, "loginOrRegister": {"message": "Login / Register", "description": "Button text for user login or registration."}, "logout": {"message": "Logout", "description": "Button text for user logout."}, "clearConfig": {"message": "Clear Configuration", "description": "<PERSON><PERSON> to clear all saved AI configurations."}, "authenticatedUser": {"message": "Authenticated User"}, "userIdDisplay": {"message": "User ID: ${userId}..."}, "loginModalTitle": {"message": "Login or Register", "description": "Title for the login modal."}, "emailPlaceholder": {"message": "Please enter your email", "description": "Placeholder for the email input field."}, "passwordPlaceholder": {"message": "Please enter your password", "description": "Placeholder for the password input field."}, "orDivider": {"message": "or", "description": "Divider text, e.g., 'or' between login options."}, "loginWithGoogle": {"message": "Login with Google", "description": "Button text for Google social login."}, "loginWithGithub": {"message": "Login with GitHub", "description": "Button text for GitHub social login."}, "importingBookmarks": {"message": "Importing bookmarks, please wait...", "description": "Loading message shown during bookmark import."}, "langEnglish": {"message": "English", "description": "Language option for English."}, "langChinese": {"message": "简体中文", "description": "Language option for Simplified Chinese."}, "moreTags": {"message": "+${count} more", "description": "Indicator for more tags not shown in the list, e.g., '+3 more'."}, "initializationError": {"message": "Failed to initialize the extension. Please try reloading.", "description": "Error message shown on the options page if initialization fails."}, "confirmClearAIConfig": {"message": "Are you sure you want to clear all AI configurations? This will delete the configuration on both local and server.", "description": "Confirmation message before clearing all AI settings."}, "aiConfigCleared": {"message": "AI configuration has been completely cleared (local and server).", "description": "Success toast when AI config is cleared everywhere."}, "aiConfigClearedLocalOnly": {"message": "Local AI configuration cleared, but failed to delete server configuration.", "description": "Warning toast when only local AI config could be cleared."}, "aiConfigClearFailed": {"message": "Failed to clear AI configuration: ${error}", "description": "Error toast when clearing AI config fails."}, "confirmRestartAiTasks": {"message": "Are you sure you want to force restart AI analysis tasks?\nThis will clear the existing queue and re-check all bookmarks to re-create tasks for processing, failed, or incomplete items.", "description": "Confirmation message before restarting the AI queue."}, "restartingAiTasks": {"message": "Sending restart command...", "description": "Toast message indicating the AI restart command has been sent."}, "restartAiTasksSuccess": {"message": "Successfully re-created ${count} AI analysis tasks.", "description": "Success toast after AI tasks have been restarted."}, "restartAiTasksFailed": {"message": "Operation failed: ${message}", "description": "Error toast when restarting AI tasks fails."}, "importing": {"message": "Importing...", "description": "Button text shown during an import process."}, "importStarted": {"message": "Import process started...", "description": "Toast message when the bookmark import begins."}, "qaMustEnterQuestion": {"message": "Please enter a question to ask.", "description": "Alert message if the user tries to ask an empty question."}, "qaSearchError": {"message": "An error occurred during the search.", "description": "Alert message if the Q&A search fails."}, "qaJsonParseError": {"message": "Failed to parse the AI's response. The format might be incorrect.", "description": "Error message when the AI response is not valid JSON."}, "qaTipRetry": {"message": "Try asking in a different way.", "description": "A helpful tip for the user in the Q&A section."}, "qaTipCheckConfig": {"message": "Check your AI configuration and API key.", "description": "A helpful tip for the user in the Q&A section."}, "qaAiSearchError": {"message": "AI search failed: ${message}", "description": "Error message when the AI-powered search fails."}, "qaSearchGoogle": {"message": "Search on Google for an answer.", "description": "Fallback recommendation to search on Google."}, "qaSearchBaidu": {"message": "Search on <PERSON><PERSON> for an answer.", "description": "Fallback recommendation to search on Baidu."}, "qaTipCheckNetwork": {"message": "Check your network connection.", "description": "A helpful tip for the user in the Q&A section."}, "qaTruncatedError": {"message": "AI response seems to be truncated.", "description": "Error message when the AI response is incomplete."}, "qaTipRetryComplete": {"message": "Please try again to get a complete response.", "description": "A helpful tip for the user in the Q&A section."}, "qaNoResults": {"message": "No relevant bookmarks found for your question: ${question}", "description": "Message shown when Q&A search yields no results."}, "qaSuggestions": {"message": "Suggestions", "description": "Header for a list of suggestions."}, "qaSuggestion1": {"message": "Try using different keywords.", "description": "A suggestion for the user."}, "qaSuggestion2": {"message": "Make sure your bookmarks have been analyzed by AI.", "description": "A suggestion for the user."}, "qaSuggestion3": {"message": "Ask a more general question.", "description": "A suggestion for the user."}, "qaResultsFound": {"message": "${count} relevant results found.", "description": "Header indicating the number of search results."}, "relevance": {"message": "Relevance", "description": "Label for the relevance score."}, "viewDetails": {"message": "View Details", "description": "Button text to view more details."}, "addedToBookmarksToast": {"message": "Added '${title}' to your bookmarks.", "description": "Success toast when a recommended site is bookmarked."}, "alreadyExistsToast": {"message": "This page is already in your bookmarks.", "description": "Toast message when trying to add a duplicate bookmark from recommendations."}, "addFailedToast": {"message": "Failed to add bookmark: ${message}", "description": "Error toast when adding a recommended site fails."}, "qaReasonRelated": {"message": "Found in your existing bookmarks.", "description": "Reason given for a bookmark that is already in the user's collection."}, "openingLearningAssistant": {"message": "Opening Learning Assistant...", "description": "Toast message shown when opening the learning assistant."}, "qaPromptSystem": {"message": "You are an intelligent assistant. Answer questions based on the user's bookmark preferences and recommend websites."}, "qaPromptQuestion": {"message": "Question"}, "qaPromptOverview": {"message": "User's Bookmarks Overview"}, "qaPromptTotal": {"message": "Total bookmarks"}, "qaPromptSites": {"message": "sites"}, "qaPromptMainCat": {"message": "Main categories"}, "qaPromptCommonTags": {"message": "Common tags"}, "qaPromptRelated": {"message": "User's Existing Related Bookmarks"}, "qaPromptUnclassified": {"message": "Unclassified"}, "qaPromptFormatInstructions": {"message": "Response Format (must return complete JSON, not truncated):"}, "qaPromptFormatJson": {"message": "{\"answer\":\"Brief answer\",\"recommendations\":[{\"title\":\"Site Name\",\"url\":\"Full URL\",\"description\":\"Brief description\",\"category\":\"Category\",\"tags\":[\"tag1\",\"tag2\"],\"why\":\"Brief reason\"}],\"existingBookmarks\":[],\"tips\":[\"Tip 1\",\"Tip 2\"]}"}, "qaPromptStrictReq": {"message": "Strict Requirements"}, "qaPromptReq1": {"message": "1. Must return complete JSON, ensuring it ends with }"}, "qaPromptReq2": {"message": "2. Return ONLY the JSON, no other text or markdown."}, "qaPromptReq3": {"message": "3. Recommend 3 real websites, each description no more than 25 words."}, "qaPromptReq4": {"message": "4. URL must be complete and accessible."}, "qaPromptReq5": {"message": "5. If content is too long, prioritize JSON completeness."}, "learningAssistantTitle": {"message": "🎓 Learning Assistant", "description": "Title for the Learning Assistant side panel."}, "assistantPlaceholder": {"message": "Please open the learning assistant from the bookmark list on the options page.", "description": "Placeholder text in the side panel when no bookmark is active."}, "prompt_ask_about_bookmark_in_tab": {"message": "You are a rigorous AI Q&A assistant. Please answer the user's question strictly based on the \"Context\" provided below.\n\n### Context:\n$pageContent$\n\n### User's Question:\n$question$\n\n### Your Requirements:\n- Your answer must be based entirely on the \"Context\" above.\n- If the \"Context\" does not contain enough information to answer the question, please state clearly: \"Based on the provided article content, this question cannot be answered.\"\n- The answer should be direct and concise.", "description": "Prompt for asking questions about a bookmark's content.", "placeholders": {"pageContent": {"content": "$1"}, "question": {"content": "$2"}}}, "prompt_generate_quiz_in_tab": {"message": "You are an excellent learning tutor. Please read the following \"Text Content\" carefully, extract 3 to 5 of the most important key knowledge points, and design a learning quiz.\n\n### Text Content:\n$pageContent$\n\n### Your Task:\n1. Create 3-5 questions, which can be multiple-choice or short-answer.\n2. Ensure the questions effectively test understanding of the text's core content.\n3. Return a JSON object containing a \"quiz\" list. Each question object should include \"question\" (question), \"type\" ('multiple-choice' or 'short-answer'), \"options\" (array of options for multiple-choice, empty for short-answer), and \"answer\" (answer).\n\n### JSON Format Example:\n{\"quiz\": [{\"question\": \"What is the main purpose of React Hooks?\",\"type\": \"multiple-choice\",\"options\": [\"A. To style components\",\"B. To use state and other React features in functional components\",\"C. For routing management\"],\"answer\": \"B. To use state and other React features in functional components\"}]}\n\n### Critical Instruction:\nYour response must be and only be a single, complete, syntactically correct JSON object. Do not add any extra text, explanations, or comments before or after the JSON code block. If you cannot generate a meaningful quiz from the content, you must return a JSON object with an empty list: {\"quiz\": []}", "description": "Prompt for generating a quiz from a bookmark's content.", "placeholders": {"pageContent": {"content": "$1"}}}, "myFolders": {"message": "My Folders", "description": "Title for the folders section in the sidebar."}, "smartCategories": {"message": "Smart Categories", "description": "Title for the smart categories section in the sidebar."}, "continueAnalysis": {"message": "Continue analyzing unfinished bookmarks", "description": "Tooltip for the continue analysis button."}, "reanalysis": {"message": "Re-analyze all bookmarks", "description": "Tooltip for the re-analysis button."}, "smartCategoriesLabel": {"message": "Smart Categories", "description": "Label for smart categories section in bookmark list."}, "smartCategoryFilter": {"message": "Smart Category: ${category}", "description": "Title when filtering by smart category."}, "confidenceTooltip": {"message": "AI Classification Confidence: Indicates how confident the AI is about this classification result. 85% means the AI is 85% sure this classification is correct. Higher confidence means more reliable classification.", "description": "Tooltip explanation for smart category confidence badge."}}