/* learningAssistant.css */

#learning-assistant-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-width: 90vw;
    max-height: 90vh;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 2147483647; /* 尽可能使用最大z-index */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    color: #333;
    display: flex;
    flex-direction: column;
    font-size: 14px;
}

#la-header {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
}

#la-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

#la-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #888;
    padding: 0 5px;
}
#la-close-btn:hover {
    color: #333;
}

#la-content {
    padding: 15px;
    overflow-y: auto;
}

.la-section {
    margin-bottom: 20px;
}
.la-section:last-child {
    margin-bottom: 0;
}

.la-section strong {
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
}

.la-input-group {
    display: flex;
    gap: 8px;
    align-items: flex-start;
}

#la-qa-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
}

#la-ask-btn, #la-quiz-btn {
    padding: 8px 15px;
    background: #4285f4;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

#la-ask-btn:hover, #la-quiz-btn:hover {
    background: #3367d6;
}

.la-answer-area {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.6;
}

.la-answer-area ol {
    padding-left: 20px;
    margin: 0;
}

.la-answer-area li {
    margin-bottom: 15px;
}

.la-answer-area details {
    cursor: pointer;
}

.la-answer-area summary {
    font-weight: 500;
    color: #1976d2;
}