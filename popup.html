<!DOCTYPE html>
<html lang="en" data-i18n-title="extensionName">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smart Bookmarker</title>
  <style>
    body {
      width: 400px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, sans-serif;
    }
    .header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    button {
      padding: 8px 12px;
      background: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #3367d6;
    }
    .tabs {
      display: flex;
      margin-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }
    .tab {
      padding: 8px 15px;
      cursor: pointer;
    }
    .tab.active {
      border-bottom: 2px solid #4285f4;
      font-weight: bold;
    }
    .bookmark-list {
      max-height: 400px;
      overflow-y: auto;
    }
    .bookmark-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }
    .favicon {
      width: 16px;
      height: 16px;
      margin-right: 10px;
      flex-shrink: 0;
      vertical-align: middle;
    }
    .bookmark-info {
      flex: 1;
      overflow: hidden;
    }
    .clickable {
      cursor: pointer;
      color: #4285f4;
      transition: opacity 0.2s;
    }
    .clickable:hover {
      opacity: 0.8;
      text-decoration: underline;
    }
    .bookmark-title {
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .bookmark-url {
      font-size: 12px;
      color: #666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .bookmark-category {
      font-size: 11px;
      color: #4285f4;
      font-weight: bold;
      margin-top: 3px;
    }
    .bookmark-summary {
      font-size: 11px;
      color: #666;
      margin-top: 2px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .bookmark-date {
      font-size: 10px;
      color: #999;
      margin-top: 3px;
    }
    .ai-status {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 10px;
      margin-top: 2px;
    }
    .status-icon {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    .status-processing {
      background: #4285f4;
      animation: pulse 1.5s infinite;
    }
    .status-completed {
      background: #34a853;
    }
    .status-failed {
      background: #ea4335;
    }
    @keyframes pulse {
      0% { opacity: 0.6; }
      50% { opacity: 1; }
      100% { opacity: 0.6; }
    }
    .star {
      color: #ccc;
      cursor: pointer;
      margin-left: 10px;
      font-size: 18px;
    }
    .star.starred {
      color: gold;
    }
    .empty-state {
      text-align: center;
      padding: 30px;
      color: #999;
    }
    /* 弹窗中的标签样式 */
    .bookmark-tags-popup {
      display: flex;
      flex-wrap: wrap;
      gap: 3px;
      margin: 3px 0;
    }
    
    .tag-popup {
      background: #e3f2fd;
      color: #1565c0;
      border-radius: 8px;
      padding: 1px 5px;
      font-size: 10px;
      font-weight: 500;
    }
    
    .tag-more {
      background: #f5f5f5;
      color: #666;
      border-radius: 8px;
      padding: 1px 5px;
      font-size: 10px;
    }
    
    /* 元数据样式 */
    .bookmark-meta {
      display: flex;
      gap: 8px;
      margin: 3px 0;
      font-size: 10px;
      color: #666;
    }
    
    .meta-item {
      background: #f0f0f0;
      padding: 1px 4px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="header">
    <button id="addCurrent" data-i18n="addCurrentPage"></button>
    <button id="openOptions" data-i18n="settings"></button>
  </div>

  <div class="tabs">
    <div class="tab active" data-tab="all" data-i18n="allBookmarks"></div>
    <div class="tab" data-tab="starred" data-i18n="starredBookmarks"></div>
  </div>

  <div class="bookmark-list" id="bookmarkList">
    <div class="empty-state" data-i18n="noBookmarks"></div>
  </div>

  <script src="i18n.js"></script>
  <script src="popup.js"></script>
</body>
</html>
