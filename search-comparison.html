<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索方法对比</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .comparison-container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .method-section { border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .keyword-method { border-left: 4px solid #2196F3; }
        .ai-method { border-left: 4px solid #4CAF50; }
        .pros-cons { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .pros { background: #e8f5e8; padding: 10px; border-radius: 4px; }
        .cons { background: #ffeaea; padding: 10px; border-radius: 4px; }
        .example { background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .code { font-family: monospace; background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 12px; }
        .highlight { background: #fff3cd; padding: 2px 4px; border-radius: 2px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background: #f5f5f5; }
        .feature-good { color: #4CAF50; font-weight: bold; }
        .feature-bad { color: #f44336; font-weight: bold; }
        .feature-ok { color: #FF9800; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 智能问答搜索方法对比</h1>
    
    <div class="comparison-container">
        <div class="method-section keyword-method">
            <h2>🔤 关键词匹配搜索</h2>
            <p><strong>当前实现的方法</strong></p>
            
            <h3>工作原理：</h3>
            <ol>
                <li>提取用户问题中的关键词</li>
                <li>在书签的title、summary、tags等字段中查找匹配</li>
                <li>根据匹配字段的权重计算相关度分数</li>
                <li>按分数排序返回结果</li>
            </ol>
            
            <div class="example">
                <h4>示例：</h4>
                <p><strong>问题：</strong>"pdf怎么转成ppt"</p>
                <p><strong>关键词：</strong>["pdf", "转成", "ppt", "PowerPoint"]</p>
                <p><strong>匹配逻辑：</strong>检查每个书签是否包含这些关键词</p>
            </div>
            
            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ 优点：</h4>
                    <ul>
                        <li>响应速度快（毫秒级）</li>
                        <li>不消耗API费用</li>
                        <li>离线可用</li>
                        <li>结果可预测</li>
                        <li>实现简单</li>
                    </ul>
                </div>
                <div class="cons">
                    <h4>❌ 缺点：</h4>
                    <ul>
                        <li>无法理解语义</li>
                        <li>同义词支持有限</li>
                        <li>无法处理复杂查询</li>
                        <li>依赖精确匹配</li>
                        <li>无法推理</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="method-section ai-method">
            <h2>🤖 AI智能搜索</h2>
            <p><strong>新增的AI增强方法</strong></p>
            
            <h3>工作原理：</h3>
            <ol>
                <li>将用户问题和所有书签数据发送给AI</li>
                <li>AI分析问题意图和书签内容的语义</li>
                <li>AI评估每个书签的相关度并给出理由</li>
                <li>返回智能排序的结果和推荐理由</li>
            </ol>
            
            <div class="example">
                <h4>示例：</h4>
                <p><strong>问题：</strong>"pdf怎么转成ppt"</p>
                <p><strong>AI分析：</strong>理解用户需要文档格式转换工具</p>
                <p><strong>智能匹配：</strong>找到相关工具，即使描述中没有直接包含关键词</p>
            </div>
            
            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ 优点：</h4>
                    <ul>
                        <li>理解语义和意图</li>
                        <li>处理复杂查询</li>
                        <li>提供推荐理由</li>
                        <li>支持自然语言</li>
                        <li>智能推理能力</li>
                    </ul>
                </div>
                <div class="cons">
                    <h4>❌ 缺点：</h4>
                    <ul>
                        <li>响应较慢（秒级）</li>
                        <li>消耗API费用</li>
                        <li>需要网络连接</li>
                        <li>结果可能不稳定</li>
                        <li>实现复杂</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <h2>📊 详细对比表</h2>
    <table>
        <thead>
            <tr>
                <th>特性</th>
                <th>关键词搜索</th>
                <th>AI智能搜索</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>响应速度</td>
                <td class="feature-good">极快 (&lt;100ms)</td>
                <td class="feature-ok">较慢 (1-3s)</td>
            </tr>
            <tr>
                <td>成本</td>
                <td class="feature-good">免费</td>
                <td class="feature-bad">消耗API额度</td>
            </tr>
            <tr>
                <td>离线能力</td>
                <td class="feature-good">完全离线</td>
                <td class="feature-bad">需要网络</td>
            </tr>
            <tr>
                <td>语义理解</td>
                <td class="feature-bad">无</td>
                <td class="feature-good">强</td>
            </tr>
            <tr>
                <td>复杂查询</td>
                <td class="feature-bad">不支持</td>
                <td class="feature-good">支持</td>
            </tr>
            <tr>
                <td>结果解释</td>
                <td class="feature-bad">无</td>
                <td class="feature-good">提供理由</td>
            </tr>
            <tr>
                <td>准确性</td>
                <td class="feature-ok">中等</td>
                <td class="feature-good">高</td>
            </tr>
            <tr>
                <td>可预测性</td>
                <td class="feature-good">高</td>
                <td class="feature-ok">中等</td>
            </tr>
        </tbody>
    </table>
    
    <h2>🎯 使用场景建议</h2>
    
    <div class="comparison-container">
        <div class="method-section keyword-method">
            <h3>适合关键词搜索的场景：</h3>
            <ul>
                <li>🔍 <strong>精确查找：</strong>知道具体的技术名词或品牌名</li>
                <li>⚡ <strong>快速查询：</strong>需要即时响应的场景</li>
                <li>💰 <strong>成本敏感：</strong>不想消耗API额度</li>
                <li>📱 <strong>离线使用：</strong>网络不稳定的环境</li>
                <li>🎯 <strong>简单需求：</strong>直接的关键词匹配就能满足</li>
            </ul>
            
            <div class="example">
                <h4>示例查询：</h4>
                <ul>
                    <li>"JavaScript教程"</li>
                    <li>"PDF工具"</li>
                    <li>"React文档"</li>
                </ul>
            </div>
        </div>
        
        <div class="method-section ai-method">
            <h3>适合AI搜索的场景：</h3>
            <ul>
                <li>🧠 <strong>语义查询：</strong>用自然语言描述需求</li>
                <li>🔄 <strong>复杂需求：</strong>需要理解上下文和意图</li>
                <li>💡 <strong>发现相关：</strong>找到意想不到的相关内容</li>
                <li>📚 <strong>学习探索：</strong>不确定具体要找什么</li>
                <li>🎨 <strong>创意查询：</strong>需要AI的推理和联想</li>
            </ul>
            
            <div class="example">
                <h4>示例查询：</h4>
                <ul>
                    <li>"我想学习前端开发，从哪里开始？"</li>
                    <li>"如何提高工作效率？"</li>
                    <li>"有什么好的设计灵感网站？"</li>
                </ul>
            </div>
        </div>
    </div>
    
    <h2>⚙️ 混合策略</h2>
    <div class="example">
        <h3>当前实现的智能策略：</h3>
        <ol>
            <li><strong>优先AI搜索：</strong>如果配置了AI且开启智能搜索，优先使用AI</li>
            <li><strong>自动降级：</strong>AI搜索失败时自动降级到关键词搜索</li>
            <li><strong>用户选择：</strong>用户可以在AI配置中关闭智能搜索</li>
            <li><strong>最佳体验：</strong>结合两种方法的优势，提供最佳搜索体验</li>
        </ol>
        
        <div class="code">
// 搜索策略伪代码
if (hasAIConfig && enableSmartSearch) {
    try {
        return await aiSmartSearch(question);
    } catch (error) {
        return await keywordSearch(question); // 降级
    }
} else {
    return await keywordSearch(question);
}
        </div>
    </div>
    
    <h2>🚀 未来改进方向</h2>
    <ul>
        <li><strong>混合搜索：</strong>结合关键词和AI的优势</li>
        <li><strong>缓存机制：</strong>缓存AI搜索结果，减少API调用</li>
        <li><strong>学习优化：</strong>根据用户行为优化搜索算法</li>
        <li><strong>向量搜索：</strong>使用embedding进行语义搜索</li>
        <li><strong>个性化：</strong>根据用户偏好调整搜索结果</li>
    </ul>
</body>
</html>
