<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能分类模式说明</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .mode-card { margin: 15px 0; padding: 20px; border-radius: 8px; border-left: 5px solid; }
        .continue-mode { border-left-color: #4caf50; background: #f1f8e9; }
        .reclassify-mode { border-left-color: #ff9800; background: #fff8e1; }
        .button-demo { display: inline-block; padding: 8px 12px; margin: 5px; border-radius: 4px; font-size: 16px; }
        .btn-continue { background: #e8f5e8; border: 1px solid #4caf50; }
        .btn-reclassify { background: #fff3e0; border: 1px solid #ff9800; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f5f5f5; }
        .scenario { margin: 10px 0; padding: 15px; background: #f8f9fa; border-radius: 6px; }
    </style>
</head>
<body>
    <h1>📚 智能分类模式使用指南</h1>
    
    <div class="section info">
        <h2>🎯 功能概述</h2>
        <p>智能分类功能现在提供两种不同的分类模式，满足不同的使用需求：</p>
        
        <div class="mode-card continue-mode">
            <h3><span class="button-demo btn-continue">▶️ 继续分类</span></h3>
            <p><strong>用途：</strong>对未分类或分类失败的书签进行智能分类</p>
            <p><strong>特点：</strong>增量处理，不影响已有分类结果</p>
        </div>
        
        <div class="mode-card reclassify-mode">
            <h3><span class="button-demo btn-reclassify">🔄 重新分类</span></h3>
            <p><strong>用途：</strong>清空所有现有分类，重新对所有书签进行分类</p>
            <p><strong>特点：</strong>全量处理，获得最新的分类结果</p>
        </div>
    </div>

    <div class="section success">
        <h2>📋 详细对比</h2>
        
        <table>
            <tr>
                <th>对比项目</th>
                <th>▶️ 继续分类</th>
                <th>🔄 重新分类</th>
            </tr>
            <tr>
                <td><strong>处理范围</strong></td>
                <td>
                    • 未分类的书签<br>
                    • 分类失败的书签<br>
                    • 低置信度分类（<0.3）
                </td>
                <td>
                    • 所有书签<br>
                    • 清空现有分类<br>
                    • 重新开始分类
                </td>
            </tr>
            <tr>
                <td><strong>处理时间</strong></td>
                <td>较短（只处理部分书签）</td>
                <td>较长（处理所有书签）</td>
            </tr>
            <tr>
                <td><strong>现有分类</strong></td>
                <td>保留已有的正确分类</td>
                <td>清空所有现有分类</td>
            </tr>
            <tr>
                <td><strong>API消耗</strong></td>
                <td>较少</td>
                <td>较多</td>
            </tr>
            <tr>
                <td><strong>适用场景</strong></td>
                <td>
                    • 日常维护<br>
                    • 新增书签后<br>
                    • 部分分类失败后
                </td>
                <td>
                    • 分类算法更新后<br>
                    • 分类结果不满意<br>
                    • 重新整理分类体系
                </td>
            </tr>
        </table>
    </div>

    <div class="section warning">
        <h2>🎯 使用场景指南</h2>
        
        <div class="scenario">
            <h4>场景1：首次启用智能分类</h4>
            <p><strong>推荐：</strong><span class="button-demo btn-continue">▶️ 继续分类</span></p>
            <p><strong>原因：</strong>所有书签都是未分类状态，继续分类会处理所有书签</p>
        </div>
        
        <div class="scenario">
            <h4>场景2：添加了新书签</h4>
            <p><strong>推荐：</strong><span class="button-demo btn-continue">▶️ 继续分类</span></p>
            <p><strong>原因：</strong>只需要对新书签进行分类，不影响已有分类</p>
        </div>
        
        <div class="scenario">
            <h4>场景3：部分分类失败或不准确</h4>
            <p><strong>推荐：</strong><span class="button-demo btn-continue">▶️ 继续分类</span></p>
            <p><strong>原因：</strong>重新处理失败和低置信度的分类，保留正确的分类</p>
        </div>
        
        <div class="scenario">
            <h4>场景4：对整体分类结果不满意</h4>
            <p><strong>推荐：</strong><span class="button-demo btn-reclassify">🔄 重新分类</span></p>
            <p><strong>原因：</strong>清空所有分类，重新开始，获得全新的分类体系</p>
        </div>
        
        <div class="scenario">
            <h4>场景5：更新了AI配置或模型</h4>
            <p><strong>推荐：</strong><span class="button-demo btn-reclassify">🔄 重新分类</span></p>
            <p><strong>原因：</strong>新的AI模型可能有更好的分类效果，值得重新分类</p>
        </div>
    </div>

    <div class="section info">
        <h2>🔧 技术实现细节</h2>
        
        <h3>继续分类模式</h3>
        <div class="code">
// 筛选需要分类的书签
const targetBookmarks = bookmarks.filter(bookmark => 
  bookmark.type === 'bookmark' && 
  (!bookmark.smartCategories || 
   bookmark.smartCategories.length === 0 ||
   bookmark.smartCategoriesConfidence < 0.3)
);
        </div>
        
        <h3>重新分类模式</h3>
        <div class="code">
// 清空现有分类
targetBookmarks.forEach(bookmark => {
  bookmark.smartCategories = [];
  bookmark.smartCategoriesUpdated = null;
  bookmark.smartCategoriesVersion = 0;
  bookmark.smartCategoriesConfidence = null;
});

// 处理所有书签
const targetBookmarks = bookmarks.filter(bookmark => 
  bookmark.type === 'bookmark'
);
        </div>
    </div>

    <div class="section success">
        <h2>✅ 最佳实践建议</h2>
        
        <h3>日常使用</h3>
        <ul>
            <li>✅ 优先使用<strong>继续分类</strong>进行日常维护</li>
            <li>✅ 定期检查分类结果，对不准确的分类进行手动调整</li>
            <li>✅ 新增书签后使用继续分类进行增量处理</li>
        </ul>
        
        <h3>重新整理</h3>
        <ul>
            <li>🔄 当分类体系需要重新设计时使用<strong>重新分类</strong></li>
            <li>🔄 更换AI模型或大幅调整配置后使用重新分类</li>
            <li>🔄 对整体分类效果不满意时使用重新分类</li>
        </ul>
        
        <h3>性能考虑</h3>
        <ul>
            <li>⚡ 继续分类消耗更少的API调用和时间</li>
            <li>⚡ 重新分类适合在网络条件好、时间充裕时使用</li>
            <li>⚡ 大量书签时建议分批进行重新分类</li>
        </ul>
    </div>

    <div class="section warning">
        <h2>⚠️ 注意事项</h2>
        
        <h3>使用重新分类前请注意：</h3>
        <ul>
            <li>🚨 <strong>会清空所有现有的智能分类</strong></li>
            <li>🚨 <strong>处理时间较长，请耐心等待</strong></li>
            <li>🚨 <strong>消耗较多API调用次数</strong></li>
            <li>🚨 <strong>建议在网络稳定时进行</strong></li>
        </ul>
        
        <h3>分类过程中：</h3>
        <ul>
            <li>📊 观察进度条了解处理进度</li>
            <li>📊 查看控制台日志了解详细过程</li>
            <li>📊 如遇问题可随时停止处理</li>
        </ul>
    </div>

    <div class="section info">
        <h2>🧪 测试步骤</h2>
        
        <h3>1. 重新加载扩展</h3>
        <div class="code">chrome://extensions/ → 重新加载扩展</div>
        
        <h3>2. 打开设置页面</h3>
        <div class="code">右键扩展图标 → 选项</div>
        
        <h3>3. 查看智能分类区域</h3>
        <p>应该看到两个按钮：</p>
        <ul>
            <li><span class="button-demo btn-continue">▶️</span> 继续分类未完成的书签</li>
            <li><span class="button-demo btn-reclassify">🔄</span> 重新分类所有书签</li>
        </ul>
        
        <h3>4. 测试不同模式</h3>
        <ol>
            <li>先点击"继续分类"测试增量处理</li>
            <li>观察处理的书签数量和时间</li>
            <li>再点击"重新分类"测试全量处理</li>
            <li>对比两种模式的效果差异</li>
        </ol>
    </div>

    <script>
        console.log('📚 智能分类双模式功能已实现！');
        console.log('🎯 功能特点：');
        console.log('  ▶️ 继续分类：增量处理，保留已有分类');
        console.log('  🔄 重新分类：全量处理，清空重新开始');
        console.log('✅ 根据使用场景选择合适的分类模式');
    </script>
</body>
</html>
