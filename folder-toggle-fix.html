<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹折叠功能修复</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; font-size: 12px; }
        .before-after { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .before { background: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #f44336; }
        .after { background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50; }
        .demo { padding: 15px; background: #f8f9fa; border-radius: 8px; margin: 10px 0; border-left: 4px solid #2196f3; }
        .step { margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔧 文件夹折叠功能修复</h1>
    
    <div class="section error">
        <h2>❌ 问题分析</h2>
        <p><strong>用户反馈：</strong>点击"我的文件夹"时，下面的内容没有显示出来，折叠/展开事件没有起作用。</p>
        
        <h3>根本原因</h3>
        <ul>
            <li>🔴 <strong>选择器不匹配</strong>：HTML使用<code>.folder-section</code>，但JavaScript查找<code>.folders-section</code></li>
            <li>🔴 <strong>重复函数定义</strong>：存在两个<code>toggleSection</code>函数，可能造成冲突</li>
            <li>🔴 <strong>图标更新缺失</strong>：没有正确更新切换图标（▶ ↔ ▼）</li>
        </ul>
    </div>

    <div class="section success">
        <h2>✅ 修复方案</h2>
        
        <h3>1. 修正选择器匹配</h3>
        <div class="before-after">
            <div class="before">
                <h4>修复前（错误）</h4>
                <div class="code">
// JavaScript选择器
const header = document.querySelector(`.folders-section .section-header`);
const content = document.querySelector(`.folders-section .section-content`);

// HTML结构
&lt;div class="folder-section"&gt;
  &lt;div class="section-header"&gt;...&lt;/div&gt;
&lt;/div&gt;
                </div>
                <p><strong>问题：</strong>选择器不匹配，找不到元素</p>
            </div>
            
            <div class="after">
                <h4>修复后（正确）</h4>
                <div class="code">
// JavaScript选择器
const header = document.querySelector('.folder-section .section-header');
const content = document.querySelector('.folder-section .section-content');

// HTML结构
&lt;div class="folder-section"&gt;
  &lt;div class="section-header"&gt;...&lt;/div&gt;
&lt;/div&gt;
                </div>
                <p><strong>解决：</strong>选择器完全匹配HTML结构</p>
            </div>
        </div>
        
        <h3>2. 专用折叠函数</h3>
        <div class="code">
// 新的专用函数
function toggleFolderSection() {
    const header = document.querySelector('.folder-section .section-header');
    const content = document.querySelector('.folder-section .section-content');
    const toggleIcon = header?.querySelector('.toggle-icon');

    if (header && content && toggleIcon) {
        content.classList.toggle('collapsed');
        
        // 更新切换图标
        if (content.classList.contains('collapsed')) {
            toggleIcon.textContent = '▶';
        } else {
            toggleIcon.textContent = '▼';
        }
    }
}
        </div>
        
        <h3>3. 清理重复代码</h3>
        <ul>
            <li>✅ 移除SmartCategoryManager中的重复<code>toggleSection</code>方法</li>
            <li>✅ 移除不必要的<code>window.toggleSection</code>暴露</li>
            <li>✅ 使用专门的<code>toggleFolderSection</code>函数</li>
        </ul>
    </div>

    <div class="section info">
        <h2>🔧 具体修复内容</h2>
        
        <h3>修改的代码</h3>
        <div class="code">
// 事件绑定（保持不变）
const foldersHeader = document.getElementById('folders-header');
if (foldersHeader) {
    foldersHeader.addEventListener('click', () => toggleFolderSection());
}

// 新的折叠函数
function toggleFolderSection() {
    const header = document.querySelector('.folder-section .section-header');
    const content = document.querySelector('.folder-section .section-content');
    const toggleIcon = header?.querySelector('.toggle-icon');

    if (header && content && toggleIcon) {
        content.classList.toggle('collapsed');
        
        // 更新切换图标
        if (content.classList.contains('collapsed')) {
            toggleIcon.textContent = '▶';  // 收起状态
        } else {
            toggleIcon.textContent = '▼';  // 展开状态
        }
    }
}
        </div>
        
        <h3>CSS样式（已存在，无需修改）</h3>
        <div class="code">
.section-content {
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
    overflow: hidden;
}

.section-content.collapsed {
    max-height: 0;
    opacity: 0;
}
        </div>
    </div>

    <div class="section warning">
        <h2>🧪 测试步骤</h2>
        
        <div class="step">
            <h4>步骤1：重新加载扩展</h4>
            <ol>
                <li>打开 <code>chrome://extensions/</code></li>
                <li>找到智能书签扩展</li>
                <li>点击"重新加载"按钮</li>
            </ol>
        </div>
        
        <div class="step">
            <h4>步骤2：验证初始状态</h4>
            <ol>
                <li>打开扩展设置页面</li>
                <li>检查"📂 我的文件夹"区域</li>
                <li>确认显示"▶"图标（收起状态）</li>
                <li>确认文件夹列表不可见</li>
            </ol>
        </div>
        
        <div class="step">
            <h4>步骤3：测试展开功能</h4>
            <ol>
                <li>点击"📂 我的文件夹"标题</li>
                <li>确认图标变为"▼"</li>
                <li>确认文件夹列表展开显示</li>
                <li>确认有平滑的动画过渡</li>
            </ol>
        </div>
        
        <div class="step">
            <h4>步骤4：测试收起功能</h4>
            <ol>
                <li>再次点击"📂 我的文件夹"标题</li>
                <li>确认图标变回"▶"</li>
                <li>确认文件夹列表收起隐藏</li>
                <li>确认动画过渡正常</li>
            </ol>
        </div>
    </div>

    <div class="section info">
        <h2>🎯 预期效果演示</h2>
        
        <h3>交互流程</h3>
        <div class="demo">
            <div style="border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                <div style="margin-bottom: 15px;">
                    <strong>初始状态（收起）：</strong>
                    <div style="display: flex; align-items: center; margin: 10px 0; cursor: pointer; padding: 8px; background: #f0f0f0; border-radius: 4px;">
                        <span style="margin-right: 8px;">📂</span>
                        <span style="flex: 1;">我的文件夹</span>
                        <span style="font-family: monospace;">▶</span>
                    </div>
                    <div style="color: #999; font-size: 12px; margin-left: 20px;">
                        (文件夹列表隐藏)
                    </div>
                </div>
                
                <div style="border-top: 1px solid #eee; padding-top: 15px;">
                    <strong>点击后（展开）：</strong>
                    <div style="display: flex; align-items: center; margin: 10px 0; cursor: pointer; padding: 8px; background: #e3f2fd; border-radius: 4px;">
                        <span style="margin-right: 8px;">📂</span>
                        <span style="flex: 1;">我的文件夹</span>
                        <span style="font-family: monospace;">▼</span>
                    </div>
                    <div style="margin-left: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="margin: 5px 0;">📁 工作相关</div>
                        <div style="margin: 5px 0;">📁 学习资料</div>
                        <div style="margin: 5px 0;">📁 娱乐</div>
                        <div style="margin: 5px 0;">📁 工具</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section success">
        <h2>🔍 调试方法</h2>
        
        <h3>如果仍然不工作，请检查：</h3>
        <ol>
            <li><strong>控制台错误</strong>：
                <ul>
                    <li>按F12打开开发者工具</li>
                    <li>查看Console标签是否有JavaScript错误</li>
                </ul>
            </li>
            
            <li><strong>元素选择器</strong>：
                <ul>
                    <li>在Console中输入：<code>document.querySelector('.folder-section .section-header')</code></li>
                    <li>应该返回一个DOM元素，而不是null</li>
                </ul>
            </li>
            
            <li><strong>事件绑定</strong>：
                <ul>
                    <li>在Console中输入：<code>document.getElementById('folders-header')</code></li>
                    <li>确认元素存在且有事件监听器</li>
                </ul>
            </li>
            
            <li><strong>CSS类状态</strong>：
                <ul>
                    <li>检查<code>.section-content</code>元素是否有<code>collapsed</code>类</li>
                    <li>确认CSS样式正确应用</li>
                </ul>
            </li>
        </ol>
        
        <h3>手动测试代码</h3>
        <div class="code">
// 在浏览器控制台中运行以下代码进行测试
const content = document.querySelector('.folder-section .section-content');
const icon = document.querySelector('.folder-section .toggle-icon');

// 测试展开
content.classList.remove('collapsed');
icon.textContent = '▼';

// 测试收起
content.classList.add('collapsed');
icon.textContent = '▶';
        </div>
    </div>

    <div class="section success">
        <h2>🎉 修复完成</h2>
        <p>文件夹折叠功能已修复！现在应该可以正常工作：</p>
        <ul>
            <li>✅ <strong>正确的选择器匹配</strong>：JavaScript能找到对应的HTML元素</li>
            <li>✅ <strong>专用的折叠函数</strong>：避免函数冲突和重复定义</li>
            <li>✅ <strong>图标状态同步</strong>：切换图标正确反映展开/收起状态</li>
            <li>✅ <strong>平滑的动画效果</strong>：CSS过渡动画正常工作</li>
        </ul>
        
        <p><strong>用户现在可以正常点击"我的文件夹"来展开/收起文件夹列表！</strong> 🚀</p>
    </div>

    <script>
        console.log('🔧 文件夹折叠功能修复完成！');
        console.log('📋 修复内容：');
        console.log('  ✅ 修正了选择器匹配问题');
        console.log('  ✅ 创建了专用的toggleFolderSection函数');
        console.log('  ✅ 添加了图标状态更新逻辑');
        console.log('  ✅ 清理了重复的函数定义');
        console.log('🧪 请重新加载扩展并测试文件夹折叠功能！');
    </script>
</body>
</html>
